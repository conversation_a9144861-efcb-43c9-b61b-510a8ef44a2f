package com.wexl.retail.student.medical;

import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.util.List;

@AllArgsConstructor
@Entity
@Data
@Table(name = "medical_history")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MedicalHistory extends Model {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "medical_history_id_seq")
    @SequenceGenerator(name = "medical_history_id_seq", allocationSize = 1)
    private Long id;

    private String student;
    private String studentName;
    private String classSection;
    private LocalDate dateOfBirth;
    private String bloodGroup;
    private String height;
    private String weight;
    private List<Guardian> emergencyContact;


}
