package com.wexl.retail.student.medical.controller;

import com.wexl.retail.student.medical.dto.MedicalHistoryDto;
import com.wexl.retail.student.medical.service.MedicalHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/medical-history")
public class MedicalHistoryController {

    private final MedicalHistoryService medicalHistoryService;

    /**
     * Create or update medical history for a student
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public MedicalHistoryDto.Response createOrUpdateMedicalHistory(
            @PathVariable String orgSlug,
            @Valid @RequestBody MedicalHistoryDto.Request request) {
        log.info("Request to create/update medical history for student: {} in org: {}", 
                request.getStudentId(), orgSlug);
        return medicalHistoryService.createOrUpdateMedicalHistory(orgSlug, request);
    }

    /**
     * Get medical history by student ID
     */
    @GetMapping("/student/{studentId}")
    public MedicalHistoryDto.Response getMedicalHistoryByStudentId(
            @PathVariable String orgSlug,
            @PathVariable Long studentId) {
        log.info("Request to get medical history for student: {} in org: {}", studentId, orgSlug);
        return medicalHistoryService.getMedicalHistoryByStudentId(orgSlug, studentId);
    }

    /**
     * Update medical history for a student
     */
    @PutMapping("/student/{studentId}")
    public MedicalHistoryDto.Response updateMedicalHistory(
            @PathVariable String orgSlug,
            @PathVariable Long studentId,
            @Valid @RequestBody MedicalHistoryDto.UpdateRequest request) {
        log.info("Request to update medical history for student: {} in org: {}", studentId, orgSlug);
        return medicalHistoryService.updateMedicalHistory(orgSlug, studentId, request);
    }

    /**
     * Delete medical history for a student
     */
    @DeleteMapping("/student/{studentId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteMedicalHistory(
            @PathVariable String orgSlug,
            @PathVariable Long studentId) {
        log.info("Request to delete medical history for student: {} in org: {}", studentId, orgSlug);
        medicalHistoryService.deleteMedicalHistory(orgSlug, studentId);
    }

    /**
     * Get all medical histories for an organization
     */
    @GetMapping
    public List<MedicalHistoryDto.Response> getAllMedicalHistories(
            @PathVariable String orgSlug) {
        log.info("Request to get all medical histories for org: {}", orgSlug);
        return medicalHistoryService.getAllMedicalHistories(orgSlug);
    }

    /**
     * Get medical histories by section
     */
    @GetMapping("/section/{sectionUuid}")
    public List<MedicalHistoryDto.Response> getMedicalHistoriesBySection(
            @PathVariable String orgSlug,
            @PathVariable String sectionUuid) {
        log.info("Request to get medical histories for section: {} in org: {}", sectionUuid, orgSlug);
        return medicalHistoryService.getMedicalHistoriesBySection(orgSlug, sectionUuid);
    }

    /**
     * Get medical histories by grade
     */
    @GetMapping("/grade/{gradeSlug}")
    public List<MedicalHistoryDto.Response> getMedicalHistoriesByGrade(
            @PathVariable String orgSlug,
            @PathVariable String gradeSlug) {
        log.info("Request to get medical histories for grade: {} in org: {}", gradeSlug, orgSlug);
        return medicalHistoryService.getMedicalHistoriesByGrade(orgSlug, gradeSlug);
    }

    /**
     * Search medical histories with various criteria
     */
    @PostMapping("/search")
    public List<MedicalHistoryDto.Response> searchMedicalHistories(
            @PathVariable String orgSlug,
            @RequestBody MedicalHistoryDto.SearchRequest request) {
        log.info("Request to search medical histories in org: {} with criteria: {}", orgSlug, request);
        return medicalHistoryService.searchMedicalHistories(orgSlug, request);
    }

    /**
     * Get student medical info summary
     */
    @GetMapping("/summary")
    public List<MedicalHistoryDto.StudentInfo> getStudentMedicalInfoSummary(
            @PathVariable String orgSlug) {
        log.info("Request to get student medical info summary for org: {}", orgSlug);
        return medicalHistoryService.getStudentMedicalInfoSummary(orgSlug);
    }

    /**
     * Get students by blood group
     */
    @GetMapping("/blood-group/{bloodGroup}")
    public List<MedicalHistoryDto.Response> getStudentsByBloodGroup(
            @PathVariable String orgSlug,
            @PathVariable String bloodGroup) {
        log.info("Request to get students with blood group: {} in org: {}", bloodGroup, orgSlug);
        
        MedicalHistoryDto.SearchRequest searchRequest = MedicalHistoryDto.SearchRequest.builder()
                .bloodGroup(bloodGroup)
                .build();
        
        return medicalHistoryService.searchMedicalHistories(orgSlug, searchRequest);
    }

    /**
     * Get students with specific allergies
     */
    @GetMapping("/allergies")
    public List<MedicalHistoryDto.Response> getStudentsWithAllergies(
            @PathVariable String orgSlug,
            @RequestParam String keyword) {
        log.info("Request to get students with allergies containing: {} in org: {}", keyword, orgSlug);
        
        MedicalHistoryDto.SearchRequest searchRequest = MedicalHistoryDto.SearchRequest.builder()
                .allergyKeyword(keyword)
                .build();
        
        return medicalHistoryService.searchMedicalHistories(orgSlug, searchRequest);
    }

    /**
     * Get students with specific medical conditions
     */
    @GetMapping("/conditions")
    public List<MedicalHistoryDto.Response> getStudentsWithMedicalConditions(
            @PathVariable String orgSlug,
            @RequestParam String keyword) {
        log.info("Request to get students with medical conditions containing: {} in org: {}", keyword, orgSlug);
        
        MedicalHistoryDto.SearchRequest searchRequest = MedicalHistoryDto.SearchRequest.builder()
                .conditionKeyword(keyword)
                .build();
        
        return medicalHistoryService.searchMedicalHistories(orgSlug, searchRequest);
    }
}
