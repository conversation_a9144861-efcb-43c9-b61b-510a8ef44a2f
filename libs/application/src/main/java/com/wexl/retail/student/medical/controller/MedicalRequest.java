package com.wexl.retail.student.medical.controller;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import lombok.Builder;

import java.util.List;

public record MedicalRequest() {
    @Builder
    public record request(
            @JsonProperty("student_id") Long studentId,
            @JsonProperty("student_name") String studentName,
            @JsonProperty("class_section") String classSection,
            @JsonProperty("date_of_birth") String dateOfBirth,
            @JsonProperty("blood_group") String bloodGroup,
            @JsonProperty("height_cm") String height,
            @JsonProperty("weight_kg") String weight,
            @JsonProperty("guardians") GuardianRequest guardians,
            @JsonProperty("allergies") List<Allergies> allergies,
            @JsonProperty("illness") List<Illness> illness) {}
    @Builder
    public record Allergies(
            @JsonProperty("bronchial_asthma") Boolean bronchialAsthma,
            @JsonProperty("headaches") Boolean headaches,
            @JsonProperty("vomitings") Boolean vomitings,
            @JsonProperty("urticaria") Boolean urticaria,
            @JsonProperty("convulsions") Boolean convulsions,
            @JsonProperty("tonsillitis") Boolean tonsillitis,
            @JsonProperty("anaemia") Boolean anaemia,
            @JsonProperty("sinusitis") Boolean sinusitis,
            @JsonProperty("bronchitis") Boolean bronchitis,
            @JsonProperty("tuberculosis") Boolean tuberculosis,
            @JsonProperty("pneumonia") Boolean pneumonia,
            @JsonProperty("epilepsy_any_other") Boolean epilepsyAnyOther,
            @JsonProperty("any_other") String anyOther) {}
    @Builder
    public record Illness(
            @JsonProperty("chickenpox") Boolean chickenpox,
            @JsonProperty("measles") Boolean measles,
            @JsonProperty("mumps") Boolean mumps,
            @JsonProperty("dizziness_fainting") Boolean dizzinessFainting,
            @JsonProperty("convulsions_due_to_fever") Boolean convulsionsDueToFever,
            @JsonProperty("epilepsy") Boolean epilepsy,
            @JsonProperty("kidney_disease") Boolean kidneyDisease,
            @JsonProperty("spondylitis_orthopaedic_back_injury") Boolean spondylitisOrthopaedicBackInjury,
            @JsonProperty("skin_infection") Boolean skinInfection) {}
}
