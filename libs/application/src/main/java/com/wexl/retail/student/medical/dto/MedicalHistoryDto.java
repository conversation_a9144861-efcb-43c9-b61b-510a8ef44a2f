package com.wexl.retail.student.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

public class MedicalHistoryDto {

    @Builder
    @Data
    public static class Request {
        @JsonProperty("student_id")
        @NotNull(message = "Student ID is required")
        private Long studentId;

        @JsonProperty("student_name")
        private String studentName;

        @JsonProperty("class_section")
        private String classSection;

        @JsonProperty("date_of_birth")
        private LocalDate dateOfBirth;

        @JsonProperty("blood_group")
        private String bloodGroup;

        @JsonProperty("height_cm")
        private String height;

        @JsonProperty("weight_kg")
        private String weight;

        @JsonProperty("allergies")
        private String allergies;

        @JsonProperty("medical_conditions")
        private String medicalConditions;

        @JsonProperty("medications")
        private String medications;

        @JsonProperty("emergency_contact_name")
        private String emergencyContactName;

        @JsonProperty("emergency_contact_phone")
        private String emergencyContactPhone;

        @JsonProperty("emergency_contact_relationship")
        private String emergencyContactRelationship;

        @JsonProperty("doctor_name")
        private String doctorName;

        @JsonProperty("doctor_phone")
        private String doctorPhone;

        @JsonProperty("insurance_provider")
        private String insuranceProvider;

        @JsonProperty("insurance_policy_number")
        private String insurancePolicyNumber;

        @JsonProperty("vaccination_records")
        private String vaccinationRecords;

        @JsonProperty("special_dietary_requirements")
        private String specialDietaryRequirements;

        @JsonProperty("physical_limitations")
        private String physicalLimitations;

        @JsonProperty("remarks")
        private String remarks;
    }

    @Builder
    @Data
    public static class Response {
        @JsonProperty("id")
        private Long id;

        @JsonProperty("student_id")
        private Long studentId;

        @JsonProperty("student_name")
        private String studentName;

        @JsonProperty("class_section")
        private String classSection;

        @JsonProperty("date_of_birth")
        private LocalDate dateOfBirth;

        @JsonProperty("blood_group")
        private String bloodGroup;

        @JsonProperty("height_cm")
        private String height;

        @JsonProperty("weight_kg")
        private String weight;

        @JsonProperty("allergies")
        private String allergies;

        @JsonProperty("medical_conditions")
        private String medicalConditions;

        @JsonProperty("medications")
        private String medications;

        @JsonProperty("emergency_contact_name")
        private String emergencyContactName;

        @JsonProperty("emergency_contact_phone")
        private String emergencyContactPhone;

        @JsonProperty("emergency_contact_relationship")
        private String emergencyContactRelationship;

        @JsonProperty("doctor_name")
        private String doctorName;

        @JsonProperty("doctor_phone")
        private String doctorPhone;

        @JsonProperty("insurance_provider")
        private String insuranceProvider;

        @JsonProperty("insurance_policy_number")
        private String insurancePolicyNumber;

        @JsonProperty("vaccination_records")
        private String vaccinationRecords;

        @JsonProperty("special_dietary_requirements")
        private String specialDietaryRequirements;

        @JsonProperty("physical_limitations")
        private String physicalLimitations;

        @JsonProperty("remarks")
        private String remarks;

        @JsonProperty("created_at")
        private String createdAt;

        @JsonProperty("updated_at")
        private String updatedAt;
    }

    @Builder
    @Data
    public static class UpdateRequest {
        @JsonProperty("student_name")
        private String studentName;

        @JsonProperty("class_section")
        private String classSection;

        @JsonProperty("date_of_birth")
        private LocalDate dateOfBirth;

        @JsonProperty("blood_group")
        private String bloodGroup;

        @JsonProperty("height_cm")
        private String height;

        @JsonProperty("weight_kg")
        private String weight;

        @JsonProperty("allergies")
        private String allergies;

        @JsonProperty("medical_conditions")
        private String medicalConditions;

        @JsonProperty("medications")
        private String medications;

        @JsonProperty("emergency_contact_name")
        private String emergencyContactName;

        @JsonProperty("emergency_contact_phone")
        private String emergencyContactPhone;

        @JsonProperty("emergency_contact_relationship")
        private String emergencyContactRelationship;

        @JsonProperty("doctor_name")
        private String doctorName;

        @JsonProperty("doctor_phone")
        private String doctorPhone;

        @JsonProperty("insurance_provider")
        private String insuranceProvider;

        @JsonProperty("insurance_policy_number")
        private String insurancePolicyNumber;

        @JsonProperty("vaccination_records")
        private String vaccinationRecords;

        @JsonProperty("special_dietary_requirements")
        private String specialDietaryRequirements;

        @JsonProperty("physical_limitations")
        private String physicalLimitations;

        @JsonProperty("remarks")
        private String remarks;
    }

    @Builder
    @Data
    public static class SearchRequest {
        @JsonProperty("section_uuid")
        private String sectionUuid;

        @JsonProperty("grade_slug")
        private String gradeSlug;

        @JsonProperty("blood_group")
        private String bloodGroup;

        @JsonProperty("allergy_keyword")
        private String allergyKeyword;

        @JsonProperty("condition_keyword")
        private String conditionKeyword;
    }

    @Builder
    @Data
    public static class StudentInfo {
        @JsonProperty("student_id")
        private Long studentId;

        @JsonProperty("student_name")
        private String studentName;

        @JsonProperty("class_section")
        private String classSection;

        @JsonProperty("blood_group")
        private String bloodGroup;

        @JsonProperty("emergency_contact")
        private String emergencyContact;

        @JsonProperty("has_allergies")
        private Boolean hasAllergies;

        @JsonProperty("has_medical_conditions")
        private Boolean hasMedicalConditions;
    }
}
