package com.wexl.retail.student.medical.repository;

import com.wexl.retail.student.medical.MedicalHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MedicalHistoryRepository extends JpaRepository<MedicalHistory, Long> {

    /**
     * Find medical history by student ID
     */
    Optional<MedicalHistory> findByStudentId(Long studentId);

    /**
     * Find all medical histories for students in a specific organization
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE u.organization = :orgSlug
            AND mh.deleted_at IS NULL
            ORDER BY mh.created_at DESC
            """, nativeQuery = true)
    List<MedicalHistory> findAllByOrgSlug(@Param("orgSlug") String orgSlug);

    /**
     * Find medical histories by section
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN sections sec ON s.section_id = sec.id
            WHERE sec.uuid = CAST(:sectionUuid AS uuid)
            AND sec.organization = :orgSlug
            AND mh.deleted_at IS NULL
            ORDER BY mh.created_at DESC
            """, nativeQuery = true)
    List<MedicalHistory> findAllBySectionAndOrg(@Param("orgSlug") String orgSlug, 
                                                @Param("sectionUuid") String sectionUuid);

    /**
     * Find medical histories by grade
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN sections sec ON s.section_id = sec.id
            WHERE sec.grade_slug = :gradeSlug
            AND sec.organization = :orgSlug
            AND mh.deleted_at IS NULL
            ORDER BY mh.created_at DESC
            """, nativeQuery = true)
    List<MedicalHistory> findAllByGradeAndOrg(@Param("orgSlug") String orgSlug, 
                                             @Param("gradeSlug") String gradeSlug);

    /**
     * Find students with specific allergies
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE u.organization = :orgSlug
            AND mh.allergies ILIKE %:allergyKeyword%
            AND mh.deleted_at IS NULL
            ORDER BY mh.created_at DESC
            """, nativeQuery = true)
    List<MedicalHistory> findByAllergiesContaining(@Param("orgSlug") String orgSlug, 
                                                  @Param("allergyKeyword") String allergyKeyword);

    /**
     * Find students with specific medical conditions
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE u.organization = :orgSlug
            AND mh.medical_conditions ILIKE %:conditionKeyword%
            AND mh.deleted_at IS NULL
            ORDER BY mh.created_at DESC
            """, nativeQuery = true)
    List<MedicalHistory> findByMedicalConditionsContaining(@Param("orgSlug") String orgSlug, 
                                                          @Param("conditionKeyword") String conditionKeyword);

    /**
     * Find students by blood group
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE u.organization = :orgSlug
            AND mh.blood_group = :bloodGroup
            AND mh.deleted_at IS NULL
            ORDER BY mh.created_at DESC
            """, nativeQuery = true)
    List<MedicalHistory> findByBloodGroup(@Param("orgSlug") String orgSlug, 
                                         @Param("bloodGroup") String bloodGroup);

    /**
     * Check if medical history exists for a student
     */
    boolean existsByStudentId(Long studentId);

    /**
     * Find medical history by student ID and organization
     */
    @Query(value = """
            SELECT mh.*
            FROM medical_history mh
            JOIN students s ON mh.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE s.id = :studentId
            AND u.organization = :orgSlug
            AND mh.deleted_at IS NULL
            """, nativeQuery = true)
    Optional<MedicalHistory> findByStudentIdAndOrgSlug(@Param("studentId") Long studentId, 
                                                       @Param("orgSlug") String orgSlug);
}
