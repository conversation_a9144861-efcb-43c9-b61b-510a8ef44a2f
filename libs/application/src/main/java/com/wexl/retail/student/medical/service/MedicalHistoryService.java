package com.wexl.retail.student.medical.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.medical.MedicalHistory;
import com.wexl.retail.student.medical.dto.MedicalHistoryDto;
import com.wexl.retail.student.medical.repository.MedicalHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MedicalHistoryService {

    private final MedicalHistoryRepository medicalHistoryRepository;
    private final StudentRepository studentRepository;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Create or update medical history for a student
     */
    @Transactional
    public MedicalHistoryDto.Response createOrUpdateMedicalHistory(String orgSlug, MedicalHistoryDto.Request request) {
        log.info("Creating/updating medical history for student: {} in org: {}", request.getStudentId(), orgSlug);

        // Validate student exists and belongs to organization
        Student student = validateStudentAndOrg(request.getStudentId(), orgSlug);

        // Check if medical history already exists
        Optional<MedicalHistory> existingHistory = medicalHistoryRepository.findByStudentIdAndOrgSlug(
                request.getStudentId(), orgSlug);

        MedicalHistory medicalHistory;
        if (existingHistory.isPresent()) {
            // Update existing record
            medicalHistory = existingHistory.get();
            updateMedicalHistoryFields(medicalHistory, request);
            log.info("Updating existing medical history for student: {}", request.getStudentId());
        } else {
            // Create new record
            medicalHistory = createNewMedicalHistory(student, request);
            log.info("Creating new medical history for student: {}", request.getStudentId());
        }

        MedicalHistory savedHistory = medicalHistoryRepository.save(medicalHistory);
        return mapToResponse(savedHistory);
    }

    /**
     * Get medical history by student ID
     */
    public MedicalHistoryDto.Response getMedicalHistoryByStudentId(String orgSlug, Long studentId) {
        log.info("Fetching medical history for student: {} in org: {}", studentId, orgSlug);

        validateStudentAndOrg(studentId, orgSlug);

        Optional<MedicalHistory> medicalHistory = medicalHistoryRepository.findByStudentIdAndOrgSlug(studentId, orgSlug);
        
        if (medicalHistory.isEmpty()) {
            throw new ApiException(InternalErrorCodes.ENTITY_NOT_FOUND, "Medical history not found for student");
        }

        return mapToResponse(medicalHistory.get());
    }

    /**
     * Update medical history
     */
    @Transactional
    public MedicalHistoryDto.Response updateMedicalHistory(String orgSlug, Long studentId, 
                                                          MedicalHistoryDto.UpdateRequest request) {
        log.info("Updating medical history for student: {} in org: {}", studentId, orgSlug);

        validateStudentAndOrg(studentId, orgSlug);

        Optional<MedicalHistory> existingHistory = medicalHistoryRepository.findByStudentIdAndOrgSlug(studentId, orgSlug);
        
        if (existingHistory.isEmpty()) {
            throw new ApiException(InternalErrorCodes.ENTITY_NOT_FOUND, "Medical history not found for student");
        }

        MedicalHistory medicalHistory = existingHistory.get();
        updateMedicalHistoryFromUpdateRequest(medicalHistory, request);

        MedicalHistory savedHistory = medicalHistoryRepository.save(medicalHistory);
        return mapToResponse(savedHistory);
    }

    /**
     * Get all medical histories for an organization
     */
    public List<MedicalHistoryDto.Response> getAllMedicalHistories(String orgSlug) {
        log.info("Fetching all medical histories for org: {}", orgSlug);

        List<MedicalHistory> histories = medicalHistoryRepository.findAllByOrgSlug(orgSlug);
        return histories.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Get medical histories by section
     */
    public List<MedicalHistoryDto.Response> getMedicalHistoriesBySection(String orgSlug, String sectionUuid) {
        log.info("Fetching medical histories for section: {} in org: {}", sectionUuid, orgSlug);

        List<MedicalHistory> histories = medicalHistoryRepository.findAllBySectionAndOrg(orgSlug, sectionUuid);
        return histories.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Get medical histories by grade
     */
    public List<MedicalHistoryDto.Response> getMedicalHistoriesByGrade(String orgSlug, String gradeSlug) {
        log.info("Fetching medical histories for grade: {} in org: {}", gradeSlug, orgSlug);

        List<MedicalHistory> histories = medicalHistoryRepository.findAllByGradeAndOrg(orgSlug, gradeSlug);
        return histories.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Search medical histories
     */
    public List<MedicalHistoryDto.Response> searchMedicalHistories(String orgSlug, MedicalHistoryDto.SearchRequest request) {
        log.info("Searching medical histories in org: {} with criteria: {}", orgSlug, request);

        List<MedicalHistory> histories;

        if (StringUtils.hasText(request.getSectionUuid())) {
            histories = medicalHistoryRepository.findAllBySectionAndOrg(orgSlug, request.getSectionUuid());
        } else if (StringUtils.hasText(request.getGradeSlug())) {
            histories = medicalHistoryRepository.findAllByGradeAndOrg(orgSlug, request.getGradeSlug());
        } else if (StringUtils.hasText(request.getBloodGroup())) {
            histories = medicalHistoryRepository.findByBloodGroup(orgSlug, request.getBloodGroup());
        } else if (StringUtils.hasText(request.getAllergyKeyword())) {
            histories = medicalHistoryRepository.findByAllergiesContaining(orgSlug, request.getAllergyKeyword());
        } else if (StringUtils.hasText(request.getConditionKeyword())) {
            histories = medicalHistoryRepository.findByMedicalConditionsContaining(orgSlug, request.getConditionKeyword());
        } else {
            histories = medicalHistoryRepository.findAllByOrgSlug(orgSlug);
        }

        return histories.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Get student medical info summary
     */
    public List<MedicalHistoryDto.StudentInfo> getStudentMedicalInfoSummary(String orgSlug) {
        log.info("Fetching student medical info summary for org: {}", orgSlug);

        List<MedicalHistory> histories = medicalHistoryRepository.findAllByOrgSlug(orgSlug);
        return histories.stream()
                .map(this::mapToStudentInfo)
                .collect(Collectors.toList());
    }

    /**
     * Delete medical history
     */
    @Transactional
    public void deleteMedicalHistory(String orgSlug, Long studentId) {
        log.info("Deleting medical history for student: {} in org: {}", studentId, orgSlug);

        validateStudentAndOrg(studentId, orgSlug);

        Optional<MedicalHistory> existingHistory = medicalHistoryRepository.findByStudentIdAndOrgSlug(studentId, orgSlug);
        
        if (existingHistory.isEmpty()) {
            throw new ApiException(InternalErrorCodes.ENTITY_NOT_FOUND, "Medical history not found for student");
        }

        medicalHistoryRepository.delete(existingHistory.get());
        log.info("Medical history deleted for student: {}", studentId);
    }

    // Private helper methods

    private Student validateStudentAndOrg(Long studentId, String orgSlug) {
        Student student = studentRepository.getStudentByIdAndOrgSlug(studentId, orgSlug);
        if (student == null) {
            throw new ApiException(InternalErrorCodes.ENTITY_NOT_FOUND, "Student not found in organization");
        }
        return student;
    }

    private MedicalHistory createNewMedicalHistory(Student student, MedicalHistoryDto.Request request) {
        MedicalHistory medicalHistory = new MedicalHistory();
        medicalHistory.setStudent(student);
        updateMedicalHistoryFields(medicalHistory, request);
        return medicalHistory;
    }

    private void updateMedicalHistoryFields(MedicalHistory medicalHistory, MedicalHistoryDto.Request request) {
        medicalHistory.setStudentName(request.getStudentName());
        medicalHistory.setClassSection(request.getClassSection());
        medicalHistory.setDateOfBirth(request.getDateOfBirth());
        medicalHistory.setBloodGroup(request.getBloodGroup());
        medicalHistory.setHeight(request.getHeight());
        medicalHistory.setWeight(request.getWeight());
        medicalHistory.setAllergies(request.getAllergies());
        medicalHistory.setMedicalConditions(request.getMedicalConditions());
        medicalHistory.setMedications(request.getMedications());
        medicalHistory.setEmergencyContactName(request.getEmergencyContactName());
        medicalHistory.setEmergencyContactPhone(request.getEmergencyContactPhone());
        medicalHistory.setEmergencyContactRelationship(request.getEmergencyContactRelationship());
        medicalHistory.setDoctorName(request.getDoctorName());
        medicalHistory.setDoctorPhone(request.getDoctorPhone());
        medicalHistory.setInsuranceProvider(request.getInsuranceProvider());
        medicalHistory.setInsurancePolicyNumber(request.getInsurancePolicyNumber());
        medicalHistory.setVaccinationRecords(request.getVaccinationRecords());
        medicalHistory.setSpecialDietaryRequirements(request.getSpecialDietaryRequirements());
        medicalHistory.setPhysicalLimitations(request.getPhysicalLimitations());
        medicalHistory.setRemarks(request.getRemarks());
    }

    private void updateMedicalHistoryFromUpdateRequest(MedicalHistory medicalHistory, MedicalHistoryDto.UpdateRequest request) {
        if (request.getStudentName() != null) medicalHistory.setStudentName(request.getStudentName());
        if (request.getClassSection() != null) medicalHistory.setClassSection(request.getClassSection());
        if (request.getDateOfBirth() != null) medicalHistory.setDateOfBirth(request.getDateOfBirth());
        if (request.getBloodGroup() != null) medicalHistory.setBloodGroup(request.getBloodGroup());
        if (request.getHeight() != null) medicalHistory.setHeight(request.getHeight());
        if (request.getWeight() != null) medicalHistory.setWeight(request.getWeight());
        if (request.getAllergies() != null) medicalHistory.setAllergies(request.getAllergies());
        if (request.getMedicalConditions() != null) medicalHistory.setMedicalConditions(request.getMedicalConditions());
        if (request.getMedications() != null) medicalHistory.setMedications(request.getMedications());
        if (request.getEmergencyContactName() != null) medicalHistory.setEmergencyContactName(request.getEmergencyContactName());
        if (request.getEmergencyContactPhone() != null) medicalHistory.setEmergencyContactPhone(request.getEmergencyContactPhone());
        if (request.getEmergencyContactRelationship() != null) medicalHistory.setEmergencyContactRelationship(request.getEmergencyContactRelationship());
        if (request.getDoctorName() != null) medicalHistory.setDoctorName(request.getDoctorName());
        if (request.getDoctorPhone() != null) medicalHistory.setDoctorPhone(request.getDoctorPhone());
        if (request.getInsuranceProvider() != null) medicalHistory.setInsuranceProvider(request.getInsuranceProvider());
        if (request.getInsurancePolicyNumber() != null) medicalHistory.setInsurancePolicyNumber(request.getInsurancePolicyNumber());
        if (request.getVaccinationRecords() != null) medicalHistory.setVaccinationRecords(request.getVaccinationRecords());
        if (request.getSpecialDietaryRequirements() != null) medicalHistory.setSpecialDietaryRequirements(request.getSpecialDietaryRequirements());
        if (request.getPhysicalLimitations() != null) medicalHistory.setPhysicalLimitations(request.getPhysicalLimitations());
        if (request.getRemarks() != null) medicalHistory.setRemarks(request.getRemarks());
    }

    private MedicalHistoryDto.Response mapToResponse(MedicalHistory medicalHistory) {
        return MedicalHistoryDto.Response.builder()
                .id(medicalHistory.getId())
                .studentId(medicalHistory.getStudent().getId())
                .studentName(medicalHistory.getStudentName())
                .classSection(medicalHistory.getClassSection())
                .dateOfBirth(medicalHistory.getDateOfBirth())
                .bloodGroup(medicalHistory.getBloodGroup())
                .height(medicalHistory.getHeight())
                .weight(medicalHistory.getWeight())
                .allergies(medicalHistory.getAllergies())
                .medicalConditions(medicalHistory.getMedicalConditions())
                .medications(medicalHistory.getMedications())
                .emergencyContactName(medicalHistory.getEmergencyContactName())
                .emergencyContactPhone(medicalHistory.getEmergencyContactPhone())
                .emergencyContactRelationship(medicalHistory.getEmergencyContactRelationship())
                .doctorName(medicalHistory.getDoctorName())
                .doctorPhone(medicalHistory.getDoctorPhone())
                .insuranceProvider(medicalHistory.getInsuranceProvider())
                .insurancePolicyNumber(medicalHistory.getInsurancePolicyNumber())
                .vaccinationRecords(medicalHistory.getVaccinationRecords())
                .specialDietaryRequirements(medicalHistory.getSpecialDietaryRequirements())
                .physicalLimitations(medicalHistory.getPhysicalLimitations())
                .remarks(medicalHistory.getRemarks())
                .createdAt(medicalHistory.getCreatedAt() != null ?
                    medicalHistory.getCreatedAt().format(DATE_TIME_FORMATTER) : null)
                .updatedAt(medicalHistory.getUpdatedAt() != null ?
                    medicalHistory.getUpdatedAt().format(DATE_TIME_FORMATTER) : null)
                .build();
    }

    private MedicalHistoryDto.StudentInfo mapToStudentInfo(MedicalHistory medicalHistory) {
        return MedicalHistoryDto.StudentInfo.builder()
                .studentId(medicalHistory.getStudent().getId())
                .studentName(medicalHistory.getStudentName())
                .classSection(medicalHistory.getClassSection())
                .bloodGroup(medicalHistory.getBloodGroup())
                .emergencyContact(medicalHistory.getEmergencyContactName() + " - " +
                    medicalHistory.getEmergencyContactPhone())
                .hasAllergies(StringUtils.hasText(medicalHistory.getAllergies()))
                .hasMedicalConditions(StringUtils.hasText(medicalHistory.getMedicalConditions()))
                .build();
    }
}
